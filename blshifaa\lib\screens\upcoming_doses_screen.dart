import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/models.dart';
import '../services/services.dart';

class UpcomingDosesScreen extends StatefulWidget {
  final Patient? patient;

  const UpcomingDosesScreen({
    super.key,
    this.patient,
  });

  @override
  State<UpcomingDosesScreen> createState() => _UpcomingDosesScreenState();
}

class _UpcomingDosesScreenState extends State<UpcomingDosesScreen> {
  final DosageScheduleService _dosageScheduleService = DosageScheduleService();
  final MedicineService _medicineService = MedicineService();
  final SchedulingService _schedulingService = SchedulingService();
  
  List<DosageSchedule> _upcomingDoses = [];
  List<DosageSchedule> _todayDoses = [];
  Map<int, Medicine> _medicinesMap = {};
  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadDoses();
  }

  Future<void> _loadDoses() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // تحميل الجرعات القادمة
      _upcomingDoses = await _dosageScheduleService.getUpcomingSchedules(
        patientId: widget.patient?.id,
        hours: 24, // الجرعات خلال 24 ساعة القادمة
      );

      // تحميل جرعات اليوم
      _todayDoses = await _dosageScheduleService.getTodaySchedules(
        patientId: widget.patient?.id,
      );

      // تحميل معلومات الأدوية
      await _loadMedicinesInfo();

      setState(() {});
    } catch (e) {
      setState(() {
        _error = 'خطأ في تحميل الجرعات: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadMedicinesInfo() async {
    Set<int> medicineIds = {};
    medicineIds.addAll(_upcomingDoses.map((d) => d.medicineId));
    medicineIds.addAll(_todayDoses.map((d) => d.medicineId));

    for (int medicineId in medicineIds) {
      try {
        Medicine? medicine = await _medicineService.getMedicineById(medicineId);
        if (medicine != null) {
          _medicinesMap[medicineId] = medicine;
        }
      } catch (e) {
        print('خطأ في تحميل معلومات الدواء $medicineId: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.patient != null 
            ? 'الجرعات القادمة - ${widget.patient!.name}'
            : 'الجرعات القادمة'),
        actions: [
          IconButton(
            onPressed: _loadDoses,
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[300],
            ),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadDoses,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // جرعات اليوم
          if (_todayDoses.isNotEmpty) ...[
            const Text(
              'جرعات اليوم',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            ..._todayDoses.map((dose) => _buildDoseCard(dose, isToday: true)),
            const SizedBox(height: 24),
          ],

          // الجرعات القادمة
          if (_upcomingDoses.isNotEmpty) ...[
            const Text(
              'الجرعات القادمة (24 ساعة)',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            ..._upcomingDoses.map((dose) => _buildDoseCard(dose)),
          ],

          // حالة فارغة
          if (_todayDoses.isEmpty && _upcomingDoses.isEmpty)
            _buildEmptyState(),
        ],
      ),
    );
  }

  Widget _buildDoseCard(DosageSchedule dose, {bool isToday = false}) {
    final medicine = _medicinesMap[dose.medicineId];
    if (medicine == null) {
      return const SizedBox.shrink();
    }

    final now = DateTime.now();
    final isOverdue = dose.scheduledDateTime.isBefore(now) && !dose.isTaken;
    final isDue = dose.isDue;

    Color cardColor = Colors.white;
    Color borderColor = Colors.grey[300]!;
    
    if (isOverdue) {
      cardColor = Colors.red[50]!;
      borderColor = Colors.red[300]!;
    } else if (isDue) {
      cardColor = Colors.orange[50]!;
      borderColor = Colors.orange[300]!;
    } else if (dose.isTaken) {
      cardColor = Colors.green[50]!;
      borderColor = Colors.green[300]!;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: cardColor,
        border: Border.all(color: borderColor),
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: dose.isTaken ? Colors.green : 
                   isOverdue ? Colors.red : 
                   isDue ? Colors.orange : Theme.of(context).primaryColor,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            dose.isTaken ? Icons.check : Icons.medication,
            color: Colors.white,
          ),
        ),
        title: Text(
          medicine.name,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 16,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text('الجرعة: ${medicine.dosage}'),
            Text('الموعد: ${_formatDateTime(dose.scheduledDateTime)}'),
            if (isOverdue)
              const Text(
                'متأخرة!',
                style: TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              )
            else if (isDue)
              const Text(
                'حان الوقت!',
                style: TextStyle(
                  color: Colors.orange,
                  fontWeight: FontWeight.bold,
                ),
              ),
          ],
        ),
        trailing: dose.isTaken
            ? const Icon(Icons.check_circle, color: Colors.green)
            : ElevatedButton(
                onPressed: () => _markDoseAsTaken(dose),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                child: const Text('تم أخذها'),
              ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.schedule,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          const Text(
            'لا توجد جرعات قادمة',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'جميع الجرعات محدثة أو لا توجد أدوية مجدولة',
            style: TextStyle(fontSize: 14),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Future<void> _markDoseAsTaken(DosageSchedule dose) async {
    try {
      bool success = await _schedulingService.markDoseAsTaken(dose.id!);
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم تسجيل الجرعة بنجاح')),
        );
        _loadDoses(); // إعادة تحميل البيانات
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ في تسجيل الجرعة'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));
    final dateOnly = DateTime(dateTime.year, dateTime.month, dateTime.day);

    String dateStr;
    if (dateOnly == today) {
      dateStr = 'اليوم';
    } else if (dateOnly == tomorrow) {
      dateStr = 'غداً';
    } else {
      dateStr = DateFormat('MM/dd').format(dateTime);
    }

    return '$dateStr - ${DateFormat('HH:mm').format(dateTime)}';
  }
}
