import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/patient_provider.dart';
import '../models/models.dart';
import 'patients_screen.dart';
import 'patient_detail_screen.dart';
import 'upcoming_doses_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    // تحميل المرضى عند بدء التطبيق
    Future.microtask(() {
      context.read<PatientProvider>().loadPatients();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('بالشفا'),
        centerTitle: true,
      ),
      body: Consumer<PatientProvider>(
        builder: (context, patientProvider, child) {
          if (patientProvider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (patientProvider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red[300],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    patientProvider.error!,
                    style: const TextStyle(fontSize: 16),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      patientProvider.clearError();
                      patientProvider.loadPatients();
                    },
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          return SingleChildScrollView(
            padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.04),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // ترحيب
                Card(
                  child: Padding(
                    padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.05),
                    child: Column(
                      children: [
                        Icon(
                          Icons.medical_services,
                          size: MediaQuery.of(context).size.width * 0.12,
                          color: Theme.of(context).primaryColor,
                        ),
                        SizedBox(height: MediaQuery.of(context).size.height * 0.015),
                        Text(
                          'مرحباً بك في تطبيق بالشفا',
                          style: TextStyle(
                            fontSize: MediaQuery.of(context).size.width * 0.05,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: MediaQuery.of(context).size.height * 0.01),
                        Text(
                          'تطبيق طبي لإدارة وجدولة مواعيد الأدوية والتنبيه بها تلقائياً',
                          style: TextStyle(fontSize: MediaQuery.of(context).size.width * 0.035),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
                
                SizedBox(height: MediaQuery.of(context).size.height * 0.03),

                // إحصائيات سريعة
                Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        'عدد المرضى',
                        patientProvider.patients.length.toString(),
                        Icons.people,
                        Colors.blue,
                      ),
                    ),
                    SizedBox(width: MediaQuery.of(context).size.width * 0.03),
                    Expanded(
                      child: InkWell(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const UpcomingDosesScreen(),
                            ),
                          );
                        },
                        child: _buildStatCard(
                          'الجرعات القادمة',
                          '0', // سيتم تحديثه لاحقاً
                          Icons.schedule,
                          Colors.orange,
                        ),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 24),
                
                // قائمة المرضى
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'المرضى',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    TextButton.icon(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const PatientsScreen(),
                          ),
                        );
                      },
                      icon: const Icon(Icons.arrow_forward),
                      label: const Text('عرض الكل'),
                    ),
                  ],
                ),
                
                const SizedBox(height: 12),
                
                if (patientProvider.patients.isEmpty)
                  _buildEmptyState()
                else
                  _buildPatientsList(patientProvider.patients),
              ],
            ),
          );
        },
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const PatientsScreen(),
            ),
          );
        },
        icon: const Icon(Icons.person_add),
        label: const Text('إضافة مريض'),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(MediaQuery.of(context).size.width * 0.04),
        child: Column(
          children: [
            Icon(icon, size: MediaQuery.of(context).size.width * 0.08, color: color),
            SizedBox(height: MediaQuery.of(context).size.height * 0.01),
            Text(
              value,
              style: TextStyle(
                fontSize: MediaQuery.of(context).size.width * 0.06,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              title,
              style: TextStyle(fontSize: MediaQuery.of(context).size.width * 0.03),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Icon(
              Icons.person_add_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            const Text(
              'لا يوجد مرضى مسجلين',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'ابدأ بإضافة مريض جديد لإدارة أدويته',
              style: TextStyle(fontSize: 14),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const PatientsScreen(),
                  ),
                );
              },
              icon: const Icon(Icons.add),
              label: const Text('إضافة مريض'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPatientsList(List<Patient> patients) {
    // عرض أول 3 مرضى فقط
    final displayPatients = patients.take(3).toList();
    
    return Column(
      children: displayPatients.map((patient) {
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: Theme.of(context).primaryColor,
              child: Text(
                patient.name.isNotEmpty ? patient.name[0] : '؟',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            title: Text(patient.name),
            subtitle: Text(
              'تم الإضافة: ${_formatDate(patient.createdAt)}',
              style: const TextStyle(fontSize: 12),
            ),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => PatientDetailScreen(patient: patient),
                ),
              );
            },
          ),
        );
      }).toList(),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
