import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/models.dart';
import '../services/services.dart';

class DoseLogScreen extends StatefulWidget {
  final Patient patient;

  const DoseLogScreen({
    super.key,
    required this.patient,
  });

  @override
  State<DoseLogScreen> createState() => _DoseLogScreenState();
}

class _DoseLogScreenState extends State<DoseLogScreen> {
  final DoseLogService _doseLogService = DoseLogService();
  List<DoseLog> _doseLogs = [];
  Map<String, int> _statistics = {};
  bool _isLoading = false;
  String? _error;
  String _selectedFilter = 'all'; // all, taken, missed, pending

  @override
  void initState() {
    super.initState();
    _loadDoseLogs();
    _loadStatistics();
  }

  Future<void> _loadDoseLogs() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      List<DoseLog> logs;
      switch (_selectedFilter) {
        case 'taken':
          logs = await _doseLogService.searchDoseLogs(
            patientId: widget.patient.id!,
            status: 'taken',
          );
          break;
        case 'missed':
          logs = await _doseLogService.searchDoseLogs(
            patientId: widget.patient.id!,
            status: 'missed',
          );
          break;
        case 'pending':
          logs = await _doseLogService.searchDoseLogs(
            patientId: widget.patient.id!,
            status: 'pending',
          );
          break;
        default:
          logs = await _doseLogService.getDoseLogsByPatientId(widget.patient.id!);
      }
      
      setState(() {
        _doseLogs = logs;
      });
    } catch (e) {
      setState(() {
        _error = 'خطأ في تحميل سجل الجرعات: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadStatistics() async {
    try {
      final stats = await _doseLogService.getDoseStatistics(widget.patient.id!);
      setState(() {
        _statistics = stats;
      });
    } catch (e) {
      print('خطأ في تحميل الإحصائيات: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('سجل الجرعات - ${widget.patient.name}'),
      ),
      body: Column(
        children: [
          // الإحصائيات
          if (_statistics.isNotEmpty) _buildStatisticsCard(),
          
          // فلاتر
          _buildFilterChips(),
          
          // قائمة السجلات
          Expanded(
            child: _buildDoseLogsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsCard() {
    final total = _statistics['total'] ?? 0;
    final taken = _statistics['taken'] ?? 0;
    final missed = _statistics['missed'] ?? 0;
    final pending = _statistics['pending'] ?? 0;
    
    final complianceRate = total > 0 ? (taken / total * 100).round() : 0;

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إحصائيات الالتزام',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'المجموع',
                    total.toString(),
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'تم أخذها',
                    taken.toString(),
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'مفقودة',
                    missed.toString(),
                    Colors.red,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'معلقة',
                    pending.toString(),
                    Colors.orange,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            LinearProgressIndicator(
              value: total > 0 ? taken / total : 0,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                complianceRate >= 80 ? Colors.green : 
                complianceRate >= 60 ? Colors.orange : Colors.red,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'معدل الالتزام: $complianceRate%',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: complianceRate >= 80 ? Colors.green : 
                       complianceRate >= 60 ? Colors.orange : Colors.red,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(fontSize: 12),
        ),
      ],
    );
  }

  Widget _buildFilterChips() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            _buildFilterChip('الكل', 'all'),
            const SizedBox(width: 8),
            _buildFilterChip('تم أخذها', 'taken'),
            const SizedBox(width: 8),
            _buildFilterChip('مفقودة', 'missed'),
            const SizedBox(width: 8),
            _buildFilterChip('معلقة', 'pending'),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterChip(String label, String value) {
    final isSelected = _selectedFilter == value;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedFilter = value;
        });
        _loadDoseLogs();
      },
      selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
      checkmarkColor: Theme.of(context).primaryColor,
    );
  }

  Widget _buildDoseLogsList() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[300],
            ),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadDoseLogs,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_doseLogs.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.history,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            const Text(
              'لا يوجد سجلات جرعات',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'ستظهر سجلات الجرعات هنا عند إضافة أدوية',
              style: TextStyle(fontSize: 14),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _doseLogs.length,
      itemBuilder: (context, index) {
        final log = _doseLogs[index];
        return _buildDoseLogCard(log);
      },
    );
  }

  Widget _buildDoseLogCard(DoseLog log) {
    Color statusColor;
    IconData statusIcon;
    String statusText;

    switch (log.status) {
      case 'taken':
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        statusText = 'تم أخذها';
        break;
      case 'missed':
        statusColor = Colors.red;
        statusIcon = Icons.cancel;
        statusText = 'مفقودة';
        break;
      default:
        statusColor = Colors.orange;
        statusIcon = Icons.schedule;
        statusText = 'معلقة';
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: Icon(
          statusIcon,
          color: statusColor,
          size: 32,
        ),
        title: Text(
          log.medicineName,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text('الموعد المجدول: ${_formatDateTime(log.scheduledDateTime)}'),
            if (log.actualDateTime != null)
              Text('تم الأخذ في: ${_formatDateTime(log.actualDateTime!)}'),
            if (log.isLate)
              Text(
                'متأخر بـ ${_formatDuration(log.delayDuration!)}',
                style: const TextStyle(color: Colors.orange),
              ),
          ],
        ),
        trailing: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: statusColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            statusText,
            style: TextStyle(
              color: statusColor,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return DateFormat('yyyy/MM/dd - HH:mm').format(dateTime);
  }

  String _formatDuration(Duration duration) {
    if (duration.inHours > 0) {
      return '${duration.inHours} ساعة و ${duration.inMinutes % 60} دقيقة';
    } else {
      return '${duration.inMinutes} دقيقة';
    }
  }
}
