^C:\USERS\<USER>\DOCUMENTS\AUGMENT-PROJECTS\BLSHIFAA\BLSHIFAA\BUILD\WINDOWS\X64\CMAKEFILES\842F55E1E2317EFBD85EDB614E6A45E7\FLUTTER_WINDOWS.DLL.RULE
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=C:\flutter\flutter PROJECT_DIR=C:\Users\<USER>\Documents\augment-projects\BLSHIFAA\blshifaa FLUTTER_ROOT=C:\flutter\flutter FLUTTER_EPHEMERAL_DIR=C:\Users\<USER>\Documents\augment-projects\BLSHIFAA\blshifaa\windows\flutter\ephemeral PROJECT_DIR=C:\Users\<USER>\Documents\augment-projects\BLSHIFAA\blshifaa FLUTTER_TARGET=C:\Users\<USER>\Documents\augment-projects\BLSHIFAA\blshifaa\lib\main.dart DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=C:\Users\<USER>\Documents\augment-projects\BLSHIFAA\blshifaa\.dart_tool\package_config.json C:/flutter/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DOCUMENTS\AUGMENT-PROJECTS\BLSHIFAA\BLSHIFAA\BUILD\WINDOWS\X64\CMAKEFILES\FA019867B8BEEF889060CC21CF2423AA\FLUTTER_ASSEMBLE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DOCUMENTS\AUGMENT-PROJECTS\BLSHIFAA\BLSHIFAA\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/Documents/augment-projects/BLSHIFAA/blshifaa/windows -BC:/Users/<USER>/Documents/augment-projects/BLSHIFAA/blshifaa/build/windows/x64 --check-stamp-file C:/Users/<USER>/Documents/augment-projects/BLSHIFAA/blshifaa/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
