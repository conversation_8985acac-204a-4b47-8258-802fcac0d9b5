import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../providers/medicine_provider.dart';
import '../models/models.dart';
import '../services/services.dart';

class AddMedicineScreen extends StatefulWidget {
  final Patient patient;

  const AddMedicineScreen({
    super.key,
    required this.patient,
  });

  @override
  State<AddMedicineScreen> createState() => _AddMedicineScreenState();
}

class _AddMedicineScreenState extends State<AddMedicineScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _dosageController = TextEditingController();
  final _durationController = TextEditingController();
  
  String _selectedType = 'مسكن';
  int _dosesPerDay = 3;
  DateTime _firstDoseDateTime = DateTime.now();
  bool _hasDuration = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // تعيين وقت افتراضي للجرعة الأولى (الساعة 8 صباحاً)
    _firstDoseDateTime = DateTime(
      DateTime.now().year,
      DateTime.now().month,
      DateTime.now().day,
      8,
      0,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إضافة دواء جديد'),
      ),
      body: Consumer<MedicineProvider>(
        builder: (context, medicineProvider, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // معلومات المريض
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          CircleAvatar(
                            backgroundColor: Theme.of(context).primaryColor,
                            child: Text(
                              widget.patient.name.isNotEmpty ? widget.patient.name[0] : '؟',
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Text(
                            'إضافة دواء للمريض: ${widget.patient.name}',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // اسم الدواء مع الاقتراحات
                  const Text(
                    'اسم الدواء',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 8),
                  TextFormField(
                    controller: _nameController,
                    decoration: const InputDecoration(
                      hintText: 'أدخل اسم الدواء',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.medication),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال اسم الدواء';
                      }
                      return null;
                    },
                    onChanged: (value) {
                      if (value.isNotEmpty) {
                        medicineProvider.searchMedicineSuggestions(value);
                      } else {
                        medicineProvider.clearSuggestions();
                      }
                    },
                  ),
                  
                  // عرض الاقتراحات
                  if (medicineProvider.medicineSuggestions.isNotEmpty)
                    Container(
                      margin: const EdgeInsets.only(top: 8),
                      constraints: const BoxConstraints(maxHeight: 200),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey[300]!),
                        borderRadius: BorderRadius.circular(8),
                        color: Colors.white,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withOpacity(0.2),
                            spreadRadius: 1,
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: ListView.builder(
                        shrinkWrap: true,
                        itemCount: medicineProvider.medicineSuggestions.length,
                        itemBuilder: (context, index) {
                          final suggestion = medicineProvider.medicineSuggestions[index];
                          return ListTile(
                            dense: true,
                            leading: const Icon(Icons.medication, size: 20),
                            title: Text(
                              suggestion,
                              style: const TextStyle(fontSize: 14),
                            ),
                            onTap: () {
                              _nameController.text = suggestion;
                              medicineProvider.clearSuggestions();
                              FocusScope.of(context).nextFocus(); // الانتقال للحقل التالي
                            },
                          );
                        },
                      ),
                    ),
                  
                  const SizedBox(height: 20),
                  
                  // نوع الدواء
                  const Text(
                    'نوع الدواء',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 8),
                  DropdownButtonFormField<String>(
                    value: _selectedType,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.category),
                    ),
                    items: medicineProvider.medicineTypes.map((type) {
                      return DropdownMenuItem(
                        value: type,
                        child: Text(type),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedType = value!;
                      });
                    },
                  ),
                  
                  const SizedBox(height: 20),
                  
                  // الجرعة
                  const Text(
                    'الجرعة',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 8),
                  TextFormField(
                    controller: _dosageController,
                    decoration: const InputDecoration(
                      hintText: 'مثال: قرص واحد، 5 مل، نصف قرص',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.local_pharmacy),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال الجرعة';
                      }
                      return null;
                    },
                  ),
                  
                  const SizedBox(height: 20),
                  
                  // عدد مرات الجرعة في اليوم
                  const Text(
                    'عدد مرات الجرعة في اليوم',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey[300]!),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text('$_dosesPerDay مرات يومياً'),
                            Text('كل ${SchedulingService.calculateHoursBetweenDoses(_dosesPerDay)} ساعات'),
                          ],
                        ),
                        Slider(
                          value: _dosesPerDay.toDouble(),
                          min: 1,
                          max: 6,
                          divisions: 5,
                          label: '$_dosesPerDay',
                          onChanged: (value) {
                            setState(() {
                              _dosesPerDay = value.round();
                            });
                          },
                        ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(height: 20),
                  
                  // تاريخ ووقت أول جرعة
                  const Text(
                    'تاريخ ووقت أول جرعة',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 8),
                  InkWell(
                    onTap: _selectFirstDoseDateTime,
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey[300]!),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.access_time),
                          const SizedBox(width: 12),
                          Text(
                            DateFormat('yyyy/MM/dd - HH:mm').format(_firstDoseDateTime),
                            style: const TextStyle(fontSize: 16),
                          ),
                          const Spacer(),
                          const Icon(Icons.edit),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 20),
                  
                  // مدة العلاج (اختياري)
                  Row(
                    children: [
                      Checkbox(
                        value: _hasDuration,
                        onChanged: (value) {
                          setState(() {
                            _hasDuration = value!;
                            if (!_hasDuration) {
                              _durationController.clear();
                            }
                          });
                        },
                      ),
                      const Text(
                        'تحديد مدة العلاج (بالأيام)',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                      ),
                    ],
                  ),
                  
                  if (_hasDuration) ...[
                    const SizedBox(height: 8),
                    TextFormField(
                      controller: _durationController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        hintText: 'عدد الأيام',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.calendar_today),
                        suffixText: 'يوم',
                      ),
                      validator: (value) {
                        if (_hasDuration && (value == null || value.trim().isEmpty)) {
                          return 'يرجى إدخال مدة العلاج';
                        }
                        if (_hasDuration && int.tryParse(value!) == null) {
                          return 'يرجى إدخال رقم صحيح';
                        }
                        return null;
                      },
                    ),
                  ],
                  
                  const SizedBox(height: 32),
                  
                  // أزرار الحفظ والإلغاء
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: _isLoading ? null : () => Navigator.pop(context),
                          child: const Text('إلغاء'),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _saveMedicine,
                          child: _isLoading
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(strokeWidth: 2),
                                )
                              : const Text('حفظ الدواء'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Future<void> _selectFirstDoseDateTime() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _firstDoseDateTime,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    
    if (date != null && mounted) {
      final time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(_firstDoseDateTime),
      );
      
      if (time != null) {
        setState(() {
          _firstDoseDateTime = DateTime(
            date.year,
            date.month,
            date.day,
            time.hour,
            time.minute,
          );
        });
      }
    }
  }

  Future<void> _saveMedicine() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final success = await context.read<MedicineProvider>().addMedicine(
        patientId: widget.patient.id!,
        name: _nameController.text.trim(),
        type: _selectedType,
        dosage: _dosageController.text.trim(),
        dosesPerDay: _dosesPerDay,
        treatmentDurationDays: _hasDuration ? int.tryParse(_durationController.text) : null,
        firstDoseDateTime: _firstDoseDateTime,
      );

      if (success && mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم إضافة الدواء وجدولة الجرعات بنجاح')),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(context.read<MedicineProvider>().error ?? 'حدث خطأ في إضافة الدواء'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _dosageController.dispose();
    _durationController.dispose();
    super.dispose();
  }
}
