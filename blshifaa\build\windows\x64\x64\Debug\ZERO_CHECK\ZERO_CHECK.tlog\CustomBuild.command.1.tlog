^C:\USERS\<USER>\DOCUMENTS\AUGMENT-PROJECTS\BLSHIFAA\BLSHIFAA\BUILD\WINDOWS\X64\CMAKEFILES\AC9126A22C4A5959423D78CF72A08FCD\GENERATE.STAMP.RULE
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/Documents/augment-projects/BLSHIFAA/blshifaa/windows -BC:/Users/<USER>/Documents/augment-projects/BLSHIFAA/blshifaa/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/Users/<USER>/Documents/augment-projects/BLSHIFAA/blshifaa/build/windows/x64/blshifaa.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
