# تطبيق بالشفا 🏥

تطبيق طبي لإدارة وجدولة مواعيد الأدوية والتنبيه بها تلقائياً بدقة.

## 📱 المنصات المدعومة

- ✅ Android
- ✅ iOS
- ✅ Windows (للاختبار)
- ✅ macOS
- ✅ Linux

## 🧩 الوظائف الرئيسية

### 1. إدارة المرضى
- إضافة مرضى متعددين
- تعديل وحذف المرضى
- البحث في قائمة المرضى
- صفحة منفصلة لكل مريض تحتوي على أدويته

### 2. إدارة الأدوية
- إضافة دواء جديد مع جميع التفاصيل:
  - اسم الدواء (مع اقتراحات تلقائية)
  - نوع الدواء (مسكن، مضاد حيوي، إلخ)
  - الجرعة (قرص، مل، إلخ)
  - عدد مرات الجرعة في اليوم (1-6 مرات)
  - حساب تلقائي للساعات بين الجرعات
  - تاريخ ووقت أول جرعة
  - مدة العلاج (اختياري)

### 3. الجدولة التلقائية
- حساب تلقائي لجميع مواعيد الجرعات
- إنشاء جدول زمني كامل للعلاج
- عرض الجرعات القادمة والحالية

### 4. نظام التنبيهات والإشعارات
- إشعارات صوتية وكتابية لكل جرعة
- التنبيه يعمل حتى لو كان التطبيق مغلقاً
- إشعارات لا تُغلق إلا يدوياً
- أزرار "تم أخذ الجرعة" في الإشعار

### 5. سجل الجرعات
- تسجيل تلقائي لكل جرعة (تم أخذها/لم تؤخذ)
- إحصائيات الالتزام بالدواء
- تسجيل الجرعات المتأخرة تلقائياً
- فلترة السجلات حسب الحالة
- حساب معدل الالتزام

### 6. الاقتراحات التلقائية
- حفظ أسماء الأدوية المستخدمة سابقاً
- اقتراحات ذكية عند كتابة اسم دواء جديد
- ترتيب الاقتراحات حسب الاستخدام والتاريخ

## 🛠️ التقنيات المستخدمة

- **Flutter** - إطار العمل الرئيسي
- **Dart** - لغة البرمجة
- **SQLite** - قاعدة البيانات المحلية
- **Provider** - إدارة الحالة
- **flutter_local_notifications** - الإشعارات المحلية
- **workmanager** - المهام في الخلفية
- **intl** - تنسيق التواريخ والأوقات

## 🚀 تشغيل التطبيق

### المتطلبات
- Flutter SDK (الإصدار 3.7.0 أو أحدث)
- Android Studio أو VS Code
- محاكي Android/iOS أو جهاز فعلي

### خطوات التشغيل

1. **استنساخ المشروع:**
```bash
git clone <repository-url>
cd blshifaa
```

2. **تثبيت التبعيات:**
```bash
flutter pub get
```

3. **تشغيل التطبيق:**
```bash
# للأندرويد
flutter run -d android

# لـ iOS
flutter run -d ios

# لـ Windows (للاختبار)
flutter run -d windows
```

## 📱 الميزات المكتملة

✅ إدارة المرضى (إضافة، تعديل، حذف، بحث)
✅ إدارة الأدوية مع جميع التفاصيل
✅ الجدولة التلقائية للجرعات
✅ نظام الاقتراحات التلقائية لأسماء الأدوية
✅ سجل الجرعات مع الإحصائيات
✅ عرض الجرعات القادمة
✅ واجهة مستخدم عربية جميلة ومتجاوبة
✅ قاعدة بيانات محلية متكاملة
✅ تصميم عصري مع Material Design 3
✅ تدرجات لونية وتأثيرات بصرية جميلة
✅ تجاوب كامل مع جميع أحجام الشاشات
✅ عرض التواريخ والأوقات بنظام 12 ساعة مع أسماء الأيام بالعربية
✅ دعم كامل للغة العربية مع التوطين

## 🎨 التحسينات الجديدة في التصميم

### تصميم عصري ومتطور:
- **Material Design 3**: استخدام أحدث معايير التصميم من Google
- **تدرجات لونية جميلة**: ألوان متدرجة من الأزرق إلى البنفسجي
- **تأثيرات بصرية**: ظلال ناعمة وانتقالات سلسة
- **بطاقات حديثة**: تصميم بطاقات مع حواف مدورة وحدود ناعمة

### تجربة مستخدم محسنة:
- **تجاوب كامل**: يعمل بشكل مثالي على جميع أحجام الشاشات
- **نظام 12 ساعة**: عرض الأوقات بصيغة صباحاً/مساءً
- **أسماء الأيام بالعربية**: عرض اليوم، أمس، غداً، والأيام بأسمائها
- **واجهة عربية كاملة**: دعم كامل للغة العربية مع التوطين

### عناصر تفاعلية:
- **أزرار متدرجة**: أزرار بتدرجات لونية وتأثيرات الضغط
- **بطاقات تفاعلية**: تأثيرات hover وانتقالات سلسة
- **أيقونات حديثة**: استخدام أيقونات Material Design المدورة
- **شريط تطبيق متدرج**: شريط علوي بتدرج لوني جميل

## 🔧 الميزات المستقبلية

- [ ] تفعيل الإشعارات الكاملة مع الجدولة
- [ ] تصدير سجل الجرعات إلى PDF
- [ ] مزامنة البيانات مع السحابة
- [ ] إضافة صور للأدوية
- [ ] تقارير طبية مفصلة
- [ ] دعم أطباء متعددين

---

**تطبيق بالشفا** - صحتك في أمان 🏥💊
